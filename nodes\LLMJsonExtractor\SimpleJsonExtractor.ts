/**
 * 简化版 JSON 提取器
 * 不依赖 Tree-sitter，使用正则表达式和启发式方法
 */

export interface JsonExtractionResult {
	success: boolean;
	data?: any;
	error?: string;
	source: 'pure-json' | 'markdown-json' | 'regex-extraction' | 'manual-fix';
	originalText?: string;
	extractedText?: string;
}

export class SimpleJsonExtractor {
	/**
	 * 从文本中提取 JSON 数据
	 */
	static extractJson(text: string): JsonExtractionResult {
		if (!text || typeof text !== 'string') {
			return {
				success: false,
				error: 'Invalid input: text must be a non-empty string',
				source: 'manual-fix'
			};
		}

		// 1. 尝试直接解析为 JSON
		const directResult = this.tryDirectJsonParse(text);
		if (directResult.success) {
			return directResult;
		}

		// 2. 尝试从 Markdown 代码块中提取 JSON
		const markdownResult = this.extractFromMarkdown(text);
		if (markdownResult.success) {
			return markdownResult;
		}

		// 3. 使用正则表达式提取
		const regexResult = this.extractWithRegex(text);
		if (regexResult.success) {
			return regexResult;
		}

		// 4. 尝试修复常见的 JSON 格式问题
		const fixedResult = this.tryFixAndParse(text);
		if (fixedResult.success) {
			return fixedResult;
		}

		return {
			success: false,
			error: 'No valid JSON found in the provided text',
			source: 'manual-fix',
			originalText: text
		};
	}

	/**
	 * 尝试直接解析 JSON
	 */
	private static tryDirectJsonParse(text: string): JsonExtractionResult {
		const trimmed = text.trim();
		
		if (!trimmed.startsWith('{') && !trimmed.startsWith('[')) {
			return { success: false, error: 'Not a direct JSON', source: 'pure-json' };
		}

		try {
			const parsed = JSON.parse(trimmed);
			return {
				success: true,
				data: parsed,
				source: 'pure-json',
				originalText: text,
				extractedText: trimmed
			};
		} catch (error) {
			return {
				success: false,
				error: `JSON parse error: ${error instanceof Error ? error.message : 'Unknown error'}`,
				source: 'pure-json'
			};
		}
	}

	/**
	 * 从 Markdown 代码块中提取 JSON
	 */
	private static extractFromMarkdown(text: string): JsonExtractionResult {
		// 匹配各种 Markdown 代码块格式
		const patterns = [
			/```json\s*\n?([\s\S]*?)\n?```/gi,
			/```javascript\s*\n?([\s\S]*?)\n?```/gi,
			/```js\s*\n?([\s\S]*?)\n?```/gi,
			/```\s*\n?([\s\S]*?)\n?```/gi,
			/`([^`\n]*)`/g
		];

		for (const pattern of patterns) {
			const matches = Array.from(text.matchAll(pattern));
			
			for (const match of matches) {
				const jsonCandidate = match[1]?.trim();
				if (!jsonCandidate) continue;

				// 检查是否看起来像 JSON
				if (jsonCandidate.startsWith('{') || jsonCandidate.startsWith('[')) {
					const result = this.tryDirectJsonParse(jsonCandidate);
					if (result.success) {
						return {
							...result,
							source: 'markdown-json',
							originalText: text,
							extractedText: jsonCandidate
						};
					}
				}
			}
		}

		return { success: false, error: 'No JSON found in markdown blocks', source: 'markdown-json' };
	}

	/**
	 * 使用正则表达式提取 JSON
	 */
	private static extractWithRegex(text: string): JsonExtractionResult {
		// 使用平衡括号的方法来匹配 JSON
		const jsonCandidates = this.findBalancedJsonStrings(text);
		
		for (const candidate of jsonCandidates) {
			const result = this.tryDirectJsonParse(candidate);
			if (result.success) {
				return {
					...result,
					source: 'regex-extraction',
					originalText: text,
					extractedText: candidate
				};
			}
		}

		return { success: false, error: 'No JSON found with regex', source: 'regex-extraction' };
	}

	/**
	 * 查找平衡的 JSON 字符串
	 */
	private static findBalancedJsonStrings(text: string): string[] {
		const results: string[] = [];
		const chars = text.split('');
		
		for (let i = 0; i < chars.length; i++) {
			if (chars[i] === '{' || chars[i] === '[') {
				const startChar = chars[i];
				const endChar = startChar === '{' ? '}' : ']';
				let depth = 1;
				let inString = false;
				let escaped = false;
				let j = i + 1;
				
				while (j < chars.length && depth > 0) {
					const char = chars[j];
					
					if (escaped) {
						escaped = false;
					} else if (char === '\\' && inString) {
						escaped = true;
					} else if (char === '"' && !escaped) {
						inString = !inString;
					} else if (!inString) {
						if (char === startChar) {
							depth++;
						} else if (char === endChar) {
							depth--;
						}
					}
					
					j++;
				}
				
				if (depth === 0) {
					const candidate = text.substring(i, j);
					if (candidate.length > 2) { // 至少要有 {} 或 []
						results.push(candidate);
					}
				}
			}
		}
		
		return results;
	}

	/**
	 * 尝试修复常见的 JSON 格式问题
	 */
	private static tryFixAndParse(text: string): JsonExtractionResult {
		// 首先尝试找到可能的 JSON 片段
		const jsonCandidates = this.findBalancedJsonStrings(text);

		if (jsonCandidates.length === 0) {
			// 如果没找到平衡的括号，尝试其他方法
			const simplePatterns = [
				/\{[^{}]*\}/g,
				/\[[^\[\]]*\]/g
			];

			for (const pattern of simplePatterns) {
				const matches = Array.from(text.matchAll(pattern));
				jsonCandidates.push(...matches.map(m => m[0]));
			}
		}

		// 如果还是没找到候选，尝试更宽松的匹配
		if (jsonCandidates.length === 0) {
			// 查找看起来像 JSON 的内容
			const looserPatterns = [
				/\{[\s\S]*?\}/g,
				/\[[\s\S]*?\]/g
			];

			for (const pattern of looserPatterns) {
				const matches = Array.from(text.matchAll(pattern));
				jsonCandidates.push(...matches.map(m => m[0]));
			}
		}

		const fixes = [
			// 移除注释（先处理，避免影响其他修复）
			(t: string) => t.replace(/\/\/.*$/gm, '').replace(/\/\*[\s\S]*?\*\//g, ''),
			// 修复尾随逗号
			(t: string) => t.replace(/,(\s*[}\]])/g, '$1'),
			// 修复单引号
			(t: string) => t.replace(/'/g, '"'),
			// 修复未引用的键（更精确的匹配）
			(t: string) => t.replace(/(\w+)\s*:/g, '"$1":'),
			// 修复多行字符串中的换行符
			(t: string) => t.replace(/:\s*"([^"]*)\n([^"]*)"/, ': "$1\\n$2"'),
			// 修复 undefined 和 function
			(t: string) => t.replace(/:\s*undefined/g, ': null').replace(/:\s*function\s*\([^)]*\)\s*\{[^}]*\}/g, ': null'),
			// 清理多余的空白字符
			(t: string) => t.replace(/\s+/g, ' ').trim()
		];

		for (const candidate of jsonCandidates) {
			let currentText = candidate.trim();

			// 逐步应用修复，每次修复后都尝试解析
			for (let i = 0; i < fixes.length; i++) {
				try {
					const fixedText = fixes[i](currentText);
					const result = this.tryDirectJsonParse(fixedText);

					if (result.success) {
						return {
							...result,
							source: 'manual-fix',
							originalText: text,
							extractedText: fixedText
						};
					}

					currentText = fixedText;
				} catch (error) {
					continue;
				}
			}

			// 如果单独修复不行，尝试组合修复
			let combinedText = candidate.trim();
			for (const fix of fixes) {
				try {
					combinedText = fix(combinedText);
				} catch (error) {
					continue;
				}
			}

			const result = this.tryDirectJsonParse(combinedText);
			if (result.success) {
				return {
					...result,
					source: 'manual-fix',
					originalText: text,
					extractedText: combinedText
				};
			}
		}

		return { success: false, error: 'Unable to fix JSON format', source: 'manual-fix' };
	}

	/**
	 * 批量提取多个 JSON 对象
	 */
	static extractMultipleJson(text: string): JsonExtractionResult[] {
		const results: JsonExtractionResult[] = [];
		const jsonCandidates = this.findBalancedJsonStrings(text);
		
		for (const candidate of jsonCandidates) {
			const result = this.extractJson(candidate);
			if (result.success) {
				results.push(result);
			}
		}

		return results;
	}

	/**
	 * 提取第一个有效的 JSON 对象
	 */
	static extractFirstJson(text: string): JsonExtractionResult {
		return this.extractJson(text);
	}

	/**
	 * 检查文本是否包含 JSON
	 */
	static containsJson(text: string): boolean {
		const result = this.extractJson(text);
		return result.success;
	}

	/**
	 * 清理和格式化提取的 JSON
	 */
	static formatExtractedJson(result: JsonExtractionResult, indent: number = 2): string {
		if (!result.success || !result.data) {
			return '';
		}
		
		try {
			return JSON.stringify(result.data, null, indent);
		} catch (error) {
			return '';
		}
	}
}
