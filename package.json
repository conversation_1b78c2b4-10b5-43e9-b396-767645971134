{"name": "n8n-nodes-parallel-workflow-executor", "version": "1.3.0", "description": "n8n node for executing workflows in parallel with controlled concurrency", "keywords": ["n8n-community-node-package", "n8n", "workflow", "parallel", "executor", "concurrency"], "license": "MIT", "homepage": "https://github.com/your-username/n8n-nodes-parallel-workflow-executor", "author": {"name": "Your Name", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/your-username/n8n-nodes-parallel-workflow-executor.git"}, "engines": {"node": ">=18.10", "pnpm": ">=8.6"}, "packageManager": "pnpm@8.6.2", "main": "dist/index.js", "scripts": {"build": "tsc && gulp build:icons", "dev": "tsc --watch", "format": "prettier nodes --write", "lint": "eslint nodes package.json", "lintfix": "eslint nodes package.json --fix", "prepublishOnly": "pnpm run build && pnpm run lint -s"}, "files": ["dist"], "n8n": {"n8nNodesApiVersion": 1, "nodes": ["dist/nodes/ParallelWorkflowExecutor/ParallelWorkflowExecutor.node.js", "dist/nodes/ModalLabs/ModalLabs.node.js", "dist/nodes/LLMJsonExtractor/LLMJsonExtractor.node.js"], "credentials": ["dist/credentials/ModalLabsApi.credentials.js"]}, "devDependencies": {"@types/node": "^24.1.0", "@typescript-eslint/parser": "^8.38.0", "eslint": "^9.32.0", "eslint-plugin-n8n-nodes-base": "^1.16.3", "gulp": "^5.0.1", "prettier": "^3.6.2", "typescript": "^5.8.3"}, "dependencies": {"modal": "^0.3.15"}, "peerDependencies": {"n8n-workflow": ">=1.0.0"}}