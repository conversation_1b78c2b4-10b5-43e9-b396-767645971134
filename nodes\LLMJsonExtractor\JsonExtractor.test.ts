import { SimpleJsonExtractor } from './SimpleJsonExtractor';

/**
 * 测试 JSON 提取功能
 */
function runTests() {
	console.log('🧪 开始测试 SimpleJsonExtractor...\n');

	const testCases = [
		{
			name: '纯 JSON 对象',
			input: '{"name": "张三", "age": 30, "city": "北京"}',
			expected: { name: "张三", age: 30, city: "北京" }
		},
		{
			name: '纯 JSON 数组',
			input: '[{"id": 1, "name": "项目A"}, {"id": 2, "name": "项目B"}]',
			expected: [{ id: 1, name: "项目A" }, { id: 2, name: "项目B" }]
		},
		{
			name: 'Markdown 包裹的 JSON',
			input: `这是一个用户信息：
\`\`\`json
{
  "user": {
    "name": "李四",
    "email": "<EMAIL>"
  }
}
\`\`\`
请处理这个数据。`,
			expected: { user: { name: "李四", email: "<EMAIL>" } }
		},
		{
			name: '不标准的 JSON（单引号）',
			input: "{'name': '王五', 'status': 'active'}",
			expected: { name: "王五", status: "active" }
		},
		{
			name: '带尾随逗号的 JSON',
			input: `{
  "id": 123,
  "name": "示例",
  "active": true,
}`,
			expected: { id: 123, name: "示例", active: true }
		},
		{
			name: '带注释的 JSON',
			input: `{
  "name": "测试项目", // 项目名称
  "version": "1.0.0"
  /* 版本信息 */
}`,
			expected: { name: "测试项目", version: "1.0.0" }
		},
		{
			name: '混合内容中的 JSON',
			input: `根据您的要求，我生成了配置：{"database": {"host": "localhost", "port": 5432}, "debug": true}，请查看。`,
			expected: { database: { host: "localhost", port: 5432 }, debug: true }
		},
		{
			name: '复杂嵌套 JSON',
			input: `\`\`\`javascript
{
  "workflow": {
    "steps": [
      {"id": "step1", "type": "extract"},
      {"id": "step2", "type": "transform"}
    ]
  }
}
\`\`\``,
			expected: {
				workflow: {
					steps: [
						{ id: "step1", type: "extract" },
						{ id: "step2", type: "transform" }
					]
				}
			}
		},
		{
			name: '无效输入',
			input: '这段文字中没有任何 JSON 数据。',
			expected: null
		}
	];

	let passedTests = 0;
	let totalTests = testCases.length;

	for (const testCase of testCases) {
		console.log(`📋 测试: ${testCase.name}`);
		
		try {
			const result = SimpleJsonExtractor.extractJson(testCase.input);
			
			if (testCase.expected === null) {
				// 期望失败的测试
				if (!result.success) {
					console.log(`✅ 正确识别为无效输入`);
					passedTests++;
				} else {
					console.log(`❌ 应该失败但成功了: ${JSON.stringify(result.data)}`);
				}
			} else {
				// 期望成功的测试
				if (result.success) {
					const isEqual = JSON.stringify(result.data) === JSON.stringify(testCase.expected);
					if (isEqual) {
						console.log(`✅ 成功提取 (方法: ${result.source})`);
						passedTests++;
					} else {
						console.log(`❌ 数据不匹配`);
						console.log(`   期望: ${JSON.stringify(testCase.expected)}`);
						console.log(`   实际: ${JSON.stringify(result.data)}`);
					}
				} else {
					console.log(`❌ 提取失败: ${result.error}`);
				}
			}
		} catch (error) {
			console.log(`💥 异常: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
		
		console.log('─'.repeat(60));
	}

	console.log(`\n📊 测试结果: ${passedTests}/${totalTests} 通过 (${Math.round(passedTests/totalTests*100)}%)\n`);

	// 测试批量提取
	console.log('🔄 测试批量提取功能...');
	
	const multiJsonText = `
	这里有多个配置：
	第一个：{"name": "config1", "value": 100}
	第二个：{"name": "config2", "value": 200}
	数组：[1, 2, 3]
	`;

	const multiResults = SimpleJsonExtractor.extractMultipleJson(multiJsonText);
	console.log(`找到 ${multiResults.length} 个 JSON 对象:`);
	
	multiResults.forEach((result, index) => {
		if (result.success) {
			console.log(`  ${index + 1}. ${JSON.stringify(result.data)}`);
		}
	});

	// 测试其他工具方法
	console.log('\n🛠️ 测试工具方法...');
	
	const testJson = '{"test": true}';
	console.log(`包含JSON: ${SimpleJsonExtractor.containsJson(testJson)}`);
	console.log(`不包含JSON: ${SimpleJsonExtractor.containsJson('普通文本')}`);
	
	const formatResult = SimpleJsonExtractor.extractJson(testJson);
	if (formatResult.success) {
		console.log('格式化输出:');
		console.log(SimpleJsonExtractor.formatExtractedJson(formatResult));
	}
}

// 导出便捷函数
export function extractJsonFromText(text: string) {
	return SimpleJsonExtractor.extractJson(text);
}

export function extractAllJsonFromText(text: string) {
	return SimpleJsonExtractor.extractMultipleJson(text);
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
	runTests();
}
