{"name": "并发控制优化测试", "nodes": [{"parameters": {}, "id": "f7b1c8e0-1234-5678-9abc-def012345678", "name": "开始", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"jsCode": "// 生成测试数据\nconst testData = [];\nfor (let i = 1; i <= 20; i++) {\n  testData.push({\n    json: {\n      id: i,\n      name: `测试项目 ${i}`,\n      delay: Math.floor(Math.random() * 3000) + 1000, // 1-4秒随机延迟\n      timestamp: new Date().toISOString()\n    }\n  });\n}\n\nreturn testData;"}, "id": "a1b2c3d4-5678-9abc-def0-123456789abc", "name": "生成测试数据", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"workflowSource": "input", "workflowId": {"__rl": true, "mode": "expression", "value": "={{ $json.workflowId || 'test-workflow-id' }}"}, "dataMode": "each", "concurrency": 5, "waitForSubWorkflow": false, "onError": "continueAll", "includeMetadata": true}, "id": "b2c3d4e5-6789-abcd-ef01-23456789abcd", "name": "并发执行测试", "type": "n8n-nodes-parallel-workflow-executor.parallelWorkflowExecutor", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"jsCode": "// 分析执行结果\nconst items = $input.all();\nconst totalItems = items.length;\nconst successCount = items.filter(item => !item.json.error).length;\nconst errorCount = items.filter(item => item.json.error).length;\n\n// 计算执行时间统计\nconst executionTimes = items\n  .filter(item => item.metadata && item.metadata.subExecution)\n  .map(item => item.metadata.subExecution.executionId);\n\nconst result = {\n  json: {\n    summary: {\n      totalItems,\n      successCount,\n      errorCount,\n      successRate: `${((successCount / totalItems) * 100).toFixed(2)}%`,\n      concurrencyLevel: 5,\n      algorithm: '滑动窗口并发控制',\n      optimizationVersion: 'v1.3.0'\n    },\n    details: {\n      executionIds: executionTimes,\n      timestamp: new Date().toISOString(),\n      testDescription: '测试优化后的并发控制算法性能和稳定性'\n    },\n    performance: {\n      memoryOptimization: '减少Promise对象创建',\n      algorithmType: '滑动窗口机制',\n      resourceUtilization: '动态任务调度',\n      errorHandling: '隔离式错误处理'\n    }\n  }\n};\n\nreturn [result];"}, "id": "c3d4e5f6-789a-bcde-f012-3456789abcde", "name": "分析执行结果", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 300]}], "connections": {"开始": {"main": [[{"node": "生成测试数据", "type": "main", "index": 0}]]}, "生成测试数据": {"main": [[{"node": "并发执行测试", "type": "main", "index": 0}]]}, "并发执行测试": {"main": [[{"node": "分析执行结果", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "12345678-9abc-def0-1234-56789abcdef0", "meta": {"templateCredsSetupCompleted": true, "instanceId": "test-instance-id"}, "id": "test-concurrency-optimization", "tags": [{"createdAt": "2024-12-29T00:00:00.000Z", "updatedAt": "2024-12-29T00:00:00.000Z", "id": "tag-1", "name": "测试"}, {"createdAt": "2024-12-29T00:00:00.000Z", "updatedAt": "2024-12-29T00:00:00.000Z", "id": "tag-2", "name": "并发优化"}]}