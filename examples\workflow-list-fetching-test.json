{"name": "工作流列表获取测试", "nodes": [{"parameters": {}, "id": "f8b8c8e0-1234-5678-9abc-def012345678", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"workflowSource": "fixed", "workflowId": {"__rl": true, "value": "", "mode": "list", "cachedResultName": ""}, "maxConcurrency": 3, "dataMode": "each", "continueOnFail": false, "includeMetadata": true}, "id": "a1b2c3d4-5678-9abc-def0-123456789abc", "name": "Parallel Workflow Executor", "type": "n8n-nodes-parallel-workflow-executor.parallelWorkflowExecutor", "typeVersion": 1, "position": [460, 300]}], "connections": {"Manual Trigger": {"main": [[{"node": "Parallel Workflow Executor", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "versionId": "12345678-1234-5678-9abc-def012345678", "meta": {"templateCredsSetupCompleted": true}, "id": "87654321-4321-8765-cba9-876543210987", "tags": []}