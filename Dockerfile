# 使用官方 n8n 镜像作为基础
FROM docker.n8n.io/n8nio/n8n:latest

# 切换到 root 用户以安装依赖
USER root

# 复制 package.json 和 package-lock.json（如果存在）
COPY package*.json ./

# 安装自定义节点的依赖
RUN npm install --only=production

# 复制编译后的节点文件
COPY dist/ /home/<USER>/.n8n/custom/

# 确保文件权限正确
RUN chown -R node:node /home/<USER>/.n8n/custom/

# 切换回 node 用户
USER node

# 设置环境变量
ENV N8N_CUSTOM_EXTENSIONS="/home/<USER>/.n8n/custom"
ENV N8N_ENFORCE_SETTINGS_FILE_PERMISSIONS="false"
