{"name": "并行工作流执行器 - 动态示例", "nodes": [{"parameters": {"values": {"string": [{"name": "workflowId", "value": "123"}, {"name": "data", "value": "测试数据1"}]}, "options": {}}, "id": "input-1", "name": "输入数据1", "type": "n8n-nodes-base.set", "typeVersion": 3.3, "position": [240, 300]}, {"parameters": {"values": {"string": [{"name": "workflowId", "value": "456"}, {"name": "data", "value": "测试数据2"}]}, "options": {}}, "id": "input-2", "name": "输入数据2", "type": "n8n-nodes-base.set", "typeVersion": 3.3, "position": [240, 480]}, {"parameters": {}, "id": "merge", "name": "合并", "type": "n8n-nodes-base.merge", "typeVersion": 2.1, "position": [460, 390]}, {"parameters": {"workflowSource": "input", "workflowIdField": "workflowId", "dataMode": "each", "concurrency": 3, "onError": "continue", "waitForSubWorkflow": true, "preserveOrder": true, "includeMetadata": false}, "id": "parallel-executor", "name": "并行工作流执行器", "type": "parallelWorkflowExecutor", "typeVersion": 1, "position": [680, 390]}], "connections": {"输入数据1": {"main": [[{"node": "合并", "type": "main", "index": 0}]]}, "输入数据2": {"main": [[{"node": "合并", "type": "main", "index": 1}]]}, "合并": {"main": [[{"node": "并行工作流执行器", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": {}, "tags": [], "triggerCount": 0, "updatedAt": "2024-01-01T00:00:00.000Z", "versionId": "1"}