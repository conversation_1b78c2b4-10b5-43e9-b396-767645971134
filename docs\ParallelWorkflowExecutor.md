# 并行工作流执行器节点

## 概述

并行工作流执行器节点允许您并行执行多个工作流，支持可控的并发数量。该节点现在支持三种不同的工作流ID获取方式，提供了更大的灵活性。

## 功能特性

- **动态工作流选择**: 支持从输入数据或表达式动态获取工作流ID
- **并发控制**: 可配置的最大并行执行数量
- **错误处理**: 灵活的错误处理策略
- **数据模式**: 支持单独执行每个项目或一次性执行所有项目
- **结果排序**: 可选择保持原始顺序或扁平化结果
- **执行元数据**: 可选择包含执行元数据信息

## 工作流来源模式

### 1. 固定工作流 (fixed)
使用固定的工作流ID，可以从列表选择或直接输入ID。

**适用场景**: 
- 所有输入项目都需要使用同一个工作流处理
- 工作流ID在设计时已知

**配置**:
- 选择"固定工作流"
- 从下拉列表选择工作流或直接输入工作流ID

### 2. 从输入数据获取 (input)
从每个输入项目的指定字段中动态获取工作流ID。

**适用场景**:
- 不同的输入项目需要使用不同的工作流处理
- 工作流ID包含在输入数据中

**配置**:
- 选择"从输入数据获取"
- 指定包含工作流ID的字段名（如：`workflowId`, `workflow_id`, `id`）

**输入数据示例**:
```json
[
  {
    "workflowId": "123",
    "data": "处理数据1"
  },
  {
    "workflowId": "456", 
    "data": "处理数据2"
  }
]
```

### 3. 从表达式获取 (expression)
使用n8n表达式动态计算工作流ID。

**适用场景**:
- 需要基于复杂逻辑计算工作流ID
- 工作流ID需要从其他节点的输出中获取

**配置**:
- 选择"从表达式获取"
- 输入n8n表达式（如：`{{ $json.workflowId }}`, `{{ $item(0).$node["Previous Node"].json.id }}`）

**表达式示例**:
```javascript
// 从当前项目获取
{{ $json.workflowId }}

// 基于条件选择工作流
{{ $json.type === 'A' ? '123' : '456' }}

// 从前一个节点获取
{{ $item(0).$node["Previous Node"].json.workflowId }}
```

## 数据模式

### 每个输入项目单独执行 (each)
为每个输入项目单独执行一次工作流。这是默认模式。

**特点**:
- 每个输入项目都会触发一次工作流执行
- 支持不同项目使用不同的工作流ID
- 结果可以保持原始顺序

### 所有输入项目一次执行 (once)
将所有输入项目一次性传递给工作流执行。

**特点**:
- 所有输入项目作为一个批次传递给工作流
- 在动态模式下，所有项目必须使用相同的工作流ID
- 适合批量处理场景

## 配置参数

| 参数 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| 工作流来源 | 选项 | fixed | 工作流ID的获取方式 |
| 工作流 | 资源定位器 | - | 固定工作流的选择（仅在固定模式下显示） |
| 工作流ID字段 | 字符串 | workflowId | 输入数据中包含工作流ID的字段名 |
| 工作流ID表达式 | 字符串 | - | 用于计算工作流ID的表达式 |
| 数据模式 | 选项 | each | 如何将输入数据传递给工作流 |
| 并发数 | 数字 | 5 | 最大并行执行数量（1-50） |
| 错误处理 | 选项 | continue | 工作流执行失败时的处理方式 |
| 等待子工作流 | 布尔 | true | 是否等待子工作流完成执行 |
| 保持顺序 | 布尔 | true | 是否保持结果的原始顺序 |
| 包含执行元数据 | 布尔 | false | 是否在结果中包含执行元数据 |

## 使用示例

### 示例1: 基于输入数据的动态工作流执行

```json
// 输入数据
[
  {"workflowId": "123", "userId": "user1", "action": "process"},
  {"workflowId": "456", "userId": "user2", "action": "validate"}
]

// 配置
{
  "workflowSource": "input",
  "workflowIdField": "workflowId",
  "dataMode": "each",
  "concurrency": 3
}
```

### 示例2: 基于表达式的条件工作流选择

```json
// 输入数据
[
  {"type": "premium", "data": "重要数据"},
  {"type": "standard", "data": "普通数据"}
]

// 配置
{
  "workflowSource": "expression", 
  "workflowIdExpression": "{{ $json.type === 'premium' ? '123' : '456' }}",
  "dataMode": "each"
}
```

## 注意事项

1. **工作流ID验证**: 确保提供的工作流ID存在且可访问
2. **并发限制**: 合理设置并发数，避免系统资源过载
3. **错误处理**: 根据业务需求选择合适的错误处理策略
4. **数据模式选择**: 在"一次执行"模式下使用动态工作流时，所有项目必须使用相同的工作流ID

## 更新日志

### v1.1.0
- 新增动态工作流ID支持
- 支持从输入数据字段获取工作流ID
- 支持从表达式计算工作流ID
- 改进错误处理和验证
- 更新节点subtitle显示动态信息
