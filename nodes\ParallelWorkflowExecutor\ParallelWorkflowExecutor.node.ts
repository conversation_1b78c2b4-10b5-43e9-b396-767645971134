import {
	IExecuteFunctions,
	ILoadOptionsFunctions,
	INodeExecutionData,
	INodeType,
	INodeTypeDescription,
	INodeListSearchResult,
} from 'n8n-workflow';

export class ParallelWorkflowExecutor implements INodeType {
	description: INodeTypeDescription = {
		displayName: '并行工作流执行器',
		name: 'parallelWorkflowExecutor',
		icon: 'fa:tasks',
		group: ['transform'],
		version: 1,
		subtitle: '={{"工作流: " + $parameter["workflowId"] + " | 并发数: " + $parameter["concurrency"]}}',
		description: '并行执行工作流，支持可控的并发数量',
		defaults: {
			name: '并行工作流执行器',
			color: '#4A90E2',
		},
		inputs: ['main'],
		outputs: ['main'],
		properties: [
			{
				displayName: '工作流',
				name: 'workflowId',
				type: 'resourceLocator',
				default: { mode: 'list', value: '' },
				required: true,
				description: '要并行执行的工作流',
				modes: [
					{
						displayName: '从列表选择',
						name: 'list',
						type: 'list',
						placeholder: '选择一个工作流...',
						typeOptions: {
							searchListMethod: 'getWorkflows',
							searchable: true,
						},
					},
					{
						displayName: '通过 ID',
						name: 'id',
						type: 'string',
						placeholder: '例如: 1234',
						validation: [
							{
								type: 'regex',
								properties: {
									regex: '^[0-9]+$',
									errorMessage: '工作流 ID 必须是数字',
								},
							},
						],
					},
				],
			},
			{
				displayName: '数据模式',
				name: 'dataMode',
				type: 'options',
				options: [
					{
						name: '每个输入项目单独执行',
						value: 'each',
						description: '为每个输入项目单独执行一次工作流',
					},
					{
						name: '所有输入项目一次执行',
						value: 'once',
						description: '将所有输入项目一次性传递给工作流执行',
					},
				],
				default: 'each',
				description: '如何将输入数据传递给工作流',
			},
			{
				displayName: '并发数',
				name: 'concurrency',
				type: 'number',
				default: 5,
				typeOptions: {
					minValue: 1,
					maxValue: 50,
				},
				description: '最大并行执行数量',
			},
			{
				displayName: '错误处理',
				name: 'onError',
				type: 'options',
				options: [
					{
						name: '继续执行',
						value: 'continue',
						description: '即使某些工作流失败，也继续执行其他工作流',
					},
					{
						name: '停止所有执行',
						value: 'stopAll',
						description: '如果任何工作流失败，停止所有执行',
					},
				],
				default: 'continue',
				description: '当工作流执行失败时的处理方式',
			},
			{
				displayName: '等待子工作流',
				name: 'waitForSubWorkflow',
				type: 'boolean',
				default: true,
				description: '是否等待子工作流完成执行后再继续',
			},
			{
				displayName: '保持顺序',
				name: 'preserveOrder',
				type: 'boolean',
				default: true,
				description: '是否保持结果的原始顺序',
			},
			{
				displayName: '包含执行元数据',
				name: 'includeMetadata',
				type: 'boolean',
				default: false,
				description: '是否在结果中包含执行元数据',
			},
		],
	};

	methods = {
		listSearch: {
			async getWorkflows(this: ILoadOptionsFunctions): Promise<INodeListSearchResult> {
				// For now, return an empty list since we need to implement proper workflow fetching
				// This would typically require access to the n8n database or API
				return {
					results: [
						{
							name: 'Example Workflow',
							value: '1',
						},
					],
				};
			},
		},
	};

	async execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {
		const items = this.getInputData();
		const workflowId = this.getNodeParameter('workflowId', 0) as { mode: string; value: string };
		const dataMode = this.getNodeParameter('dataMode', 0) as string;
		const concurrency = this.getNodeParameter('concurrency', 0) as number;
		const onError = this.getNodeParameter('onError', 0) as string;
		const options = this.getNodeParameter('options', 0, {}) as {
			waitForSubWorkflow?: boolean;
			preserveOrder?: boolean;
			includeMetadata?: boolean;
		};

		const waitForSubWorkflow = options.waitForSubWorkflow ?? true;
		const preserveOrder = options.preserveOrder ?? true;
		const includeMetadata = options.includeMetadata ?? false;

		if (dataMode === 'each') {
			// Execute workflow for each input item with concurrency control
			const results: (INodeExecutionData[] | null)[] = new Array(items.length).fill(null);
			const errors: (Error | null)[] = new Array(items.length).fill(null);
			const pool: Promise<void>[] = [];
			let currentIndex = 0;
			let hasError = false;

			const executeItem = async (itemIndex: number): Promise<void> => {
				try {
					if (waitForSubWorkflow) {
						const executionResult = await this.executeWorkflow(
							{ id: workflowId.value },
							[items[itemIndex]],
						);

						if (executionResult.data?.main) {
							results[itemIndex] = executionResult.data.main[0] || [];

							if (includeMetadata && results[itemIndex]) {
								results[itemIndex]!.forEach((item) => {
									if (!item.metadata) item.metadata = {};
									(item.metadata as any).subExecution = {
										workflowId: workflowId.value,
										executionId: executionResult.executionId,
									};
								});
							}
						} else {
							results[itemIndex] = [];
						}
					} else {
						const executionResult = await this.executeWorkflow(
							{ id: workflowId.value },
							[items[itemIndex]],
						);

						results[itemIndex] = [
							{
								...items[itemIndex],
								metadata: includeMetadata ? {
									subExecution: {
										workflowId: workflowId.value,
										executionId: executionResult.executionId,
									},
								} : undefined,
							},
						];
					}
				} catch (error) {
					errors[itemIndex] = error instanceof Error ? error : new Error(String(error));

					if (onError === 'stopAll') {
						hasError = true;
					} else if (this.continueOnFail()) {
						results[itemIndex] = [
							{
								json: { error: error instanceof Error ? error.message : String(error) },
								pairedItem: { item: itemIndex },
							},
						];
					} else {
						hasError = true;
					}
				}
			};

			// Process items with concurrency control using Promise pool
			while (currentIndex < items.length || pool.length > 0) {
				// Fill the pool up to concurrency limit
				while (pool.length < concurrency && currentIndex < items.length && !hasError) {
					const itemIndex = currentIndex++;
					const promise = executeItem(itemIndex);
					pool.push(promise);
				}

				if (pool.length === 0) break;

				// Wait for at least one promise to complete
				await Promise.race(pool);

				// Remove completed promises from pool
				for (let i = pool.length - 1; i >= 0; i--) {
					const promise = pool[i];
					// Check if promise is resolved by racing it with a resolved promise
					const isResolved = await Promise.race([
						promise.then(() => true, () => true),
						Promise.resolve(false)
					]);

					if (isResolved) {
						pool.splice(i, 1);
					}
				}

				// Check if we should stop on error
				if (hasError && onError === 'stopAll') {
					break;
				}
			}

			// Handle any remaining errors
			for (let i = 0; i < errors.length; i++) {
				if (errors[i] && !this.continueOnFail() && onError === 'stopAll') {
					throw errors[i];
				}
			}

			// Prepare final results
			if (preserveOrder) {
				const finalResults: INodeExecutionData[][] = [];
				for (let i = 0; i < results.length; i++) {
					if (results[i] !== null) {
						finalResults.push(results[i]!);
					}
				}
				return finalResults;
			} else {
				// Return results flattened
				const allResults: INodeExecutionData[] = [];
				for (const result of results) {
					if (result !== null) {
						allResults.push(...result);
					}
				}
				return [allResults];
			}
		} else {
			// Execute workflow once with all items
			try {
				const executionResult = await this.executeWorkflow(
					{ id: workflowId.value },
					items,
				);

				if (!waitForSubWorkflow) {
					return [items];
				}

				if (!executionResult.data?.main) {
					return [[]];
				}

				return executionResult.data.main;
			} catch (error) {
				if (this.continueOnFail()) {
					return [
						[
							{
								json: { error: error instanceof Error ? error.message : String(error) },
								pairedItem: { item: 0 },
							},
						],
					];
				}
				throw error;
			}
		}
	}
}
