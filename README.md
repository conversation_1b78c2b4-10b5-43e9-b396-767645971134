# n8n-nodes-parallel-workflow-executor

这是一个n8n自定义节点包，包含以下节点：

1. **Parallel Workflow Executor** - 并行执行工作流，支持并发控制
2. **Modal Labs** - 与 Modal Labs 云平台交互
3. **🆕 LLM JSON Extractor** - 从大模型输出中智能提取 JSON 数据

## 🆕 新增功能：LLM JSON 提取器

### ✨ 主要特性

- 🎯 **智能提取** - 支持纯 JSON、Markdown 包裹的 JSON 和各种不标准格式
- 🔧 **自动修复** - 自动修复单引号、尾随逗号、注释等常见问题
- 📦 **批量处理** - 从文本中提取多个 JSON 对象
- 🚀 **高性能** - 纯 JavaScript 实现，无需原生编译
- 🛡️ **错误处理** - 详细的错误信息和降级策略

### 🚀 快速开始

```typescript
import { SimpleJsonExtractor } from './nodes/utils/SimpleJsonExtractor';

// 从大模型输出中提取 JSON
const llmOutput = `
根据您的要求，我生成了配置：
\`\`\`json
{"name": "项目", "version": "1.0.0"}
\`\`\`
`;

const result = SimpleJsonExtractor.extractJson(llmOutput);
if (result.success) {
    console.log('提取成功:', result.data);
    // 输出: { name: "项目", version: "1.0.0" }
}
```

### 📖 JSON 提取器文档

- [JSON 提取器使用指南](./docs/JsonExtractor.md)
- [API 参考文档](./docs/JsonExtractor.md#api-参考)
- [演示示例](./examples/json-extractor-demo.ts)

## 功能特性

### Parallel Workflow Executor
- 🚀 **并行执行**: 支持同时执行多个工作流实例
- ⚡ **并发控制**: 可配置最大并发数量（1-50）
- 🔄 **数据模式**: 支持每个输入项单独执行或所有数据一次传递
- 🛡️ **错误处理**: 可选择继续执行或在错误时停止所有执行
- 📊 **结果管理**: 支持保持原始顺序和包含执行元数据
- ⏱️ **异步控制**: 可选择等待子工作流完成或异步执行

### Modal Labs
- 🔗 **函数调用**: 调用已部署的 Modal 函数
- 🚀 **异步执行**: 异步启动 Modal 函数
- 🧪 **沙箱环境**: 创建和管理隔离的执行环境
- 🔐 **安全认证**: 支持 Modal Labs API 令牌认证

## 安装

```bash
npm install n8n-nodes-parallel-workflow-executor
```

## 节点文档

- [Parallel Workflow Executor 使用指南](docs/ParallelWorkflowExecutor.md)
- [Modal Labs 使用指南](docs/ModalLabs.md)

## 配置参数

### 基础配置

- **Workflow**: 选择要并行执行的目标工作流
- **Data Mode**: 
  - `Each Input Item Separately`: 为每个输入项单独执行工作流
  - `All Input Items at Once`: 将所有输入项一次性传递给工作流
- **Concurrency**: 最大并发执行数量（1-50，默认5）
- **On Error**: 错误处理策略
  - `Continue Execution`: 即使某些执行失败也继续其他执行
  - `Stop All Executions`: 任何执行失败时停止所有执行

### 高级选项

- **Wait for Sub-Workflow**: 是否等待子工作流完成（默认true）
- **Preserve Order**: 是否保持结果的原始顺序（默认true）
- **Include Execution Metadata**: 是否在结果中包含执行元数据（默认false）

## 使用场景

1. **批量数据处理**: 对大量数据项并行执行相同的处理流程
2. **API调用优化**: 并行调用多个API以提高效率
3. **数据同步**: 同时向多个目标系统同步数据
4. **负载分散**: 将工作负载分散到多个并行执行中

## 示例

### 场景1: 并行处理用户数据
```
输入: 100个用户记录
配置: Concurrency = 10, Data Mode = "Each Input Item Separately"
结果: 同时处理10个用户，总共需要10轮并行执行
```

### 场景2: 批量API调用
```
输入: 50个API请求参数
配置: Concurrency = 5, On Error = "Continue Execution"
结果: 最多同时进行5个API调用，失败的请求不影响其他请求
```

## 技术实现

- 使用Promise池模式控制并发数量
- 支持错误处理和结果收集
- 保持结果的原始顺序
- 兼容n8n工作流执行机制

## 注意事项

- 高并发可能会对目标系统造成压力，请合理设置并发数量
- 确保目标工作流能够正确处理传入的数据格式
- 在生产环境中建议先进行小规模测试

## 许可证

MIT

## 贡献

欢迎提交Issue和Pull Request来改进这个节点。


实现真正的工作流列表获取 - 连接到n8n的API获取实际的工作流列表
添加更多的工作流选择模式 - 支持表达式、输入数据字段等
优化并发控制算法 - 使用更简洁的实现
增强错误处理和验证 - 添加更完善的输入验证和错误信息
支持更灵活的输出格式 - 允许用户自定义结果的组织方式